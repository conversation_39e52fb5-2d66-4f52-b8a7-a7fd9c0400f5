# 页面状态持久化功能实现

## 任务概述
基于Pinia Store的集中式状态管理方案，为`src/views/hrm/roster_entry-pending/index.vue`页面实现状态持久化功能。

## 实现计划
1. ✅ 创建页面状态管理Store (`src/store/modules/page-state/index.ts`)
2. ✅ 创建URL参数同步Hook (`src/hooks/common/useUrlSync.ts`)
3. ✅ 创建页面状态持久化Hook (`src/hooks/common/usePageStatePersistence.ts`)
4. ✅ 添加类型定义 (`src/typings/page-state.d.ts`)
5. ✅ 修改目标页面集成功能
6. ✅ 添加Store到全局配置

## 核心功能
- **Session Store状态管理**: 使用sessionStorage保存页面状态
- **URL参数读取**: 页面初始化时读取URL查询参数并映射到表单字段（仅读取，不同步）
- **状态恢复与自动搜索**: 页面初始化时自动恢复搜索状态并触发搜索

## 技术实现要点
- 使用Pinia Store进行集中式状态管理
- 利用sessionStorage实现状态持久化
- 支持从URL参数读取并初始化表单字段（单向读取）
- 支持多页面状态隔离
- 自动处理参数类型转换

## 配置说明
在目标页面中配置了以下持久化字段：
- `name`: 员工姓名 (string)
- `positionId`: 职位ID (number)
- `subStatus`: 状态 (number)
- `positionProperty`: 岗位属性 (number)
- `deptId`: 部门ID (number)

## 使用方式
```typescript
const pageStatePersistence = usePageStatePersistence(searchForm, {
  urlSync: {
    syncFields: ['name', 'positionId', 'subStatus', 'positionProperty', 'deptId'],
    fieldTypes: {
      positionId: 'number',
      subStatus: 'number',
      positionProperty: 'number',
      deptId: 'number'
    },
    enabled: true // 仅用于从URL读取初始参数，不同步表单变化到URL
  },
  autoRestore: true,
  autoSearch: true
});
```

## 预期效果
- 用户在页面进行搜索后，刷新页面或从其他页面返回时，搜索条件和结果自动恢复
- 页面初始化时可从URL参数读取并设置表单字段值
- 提供可复用的Hook，其他页面可快速集成相同功能
