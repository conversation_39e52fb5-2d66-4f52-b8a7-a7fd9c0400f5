<script setup lang="ts">
import { useFullscreen } from '@vueuse/core';
import { onMounted } from 'vue';

import GlobalBreadcrumb from '../global-breadcrumb/index.vue';
import GlobalLogo from '../global-logo/index.vue';

import ThemeButton from './components/theme-button.vue';
import UserAvatar from './components/user-avatar.vue';
import VersionModel from './components/version.vue';

import { renderModal } from '@/components/re-modal';
import { GLOBAL_HEADER_MENU_ID } from '@/constants/app';
import changePwd from '@/layouts/modules/global-header/components/change-pwd.vue';
import { fetchGetLastVersion } from '@/service/api/system/version';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { useThemeStore } from '@/store/modules/theme';
import { localStg } from '@/utils/storage';
import { groupByPurposes } from '@/views/system/version/utils';

defineOptions({
  name: 'GlobalHeader'
});

interface Props {
  /** Whether to show the logo */
  showLogo?: App.Global.HeaderProps['showLogo'];
  /** Whether to show the menu toggler */
  showMenuToggler?: App.Global.HeaderProps['showMenuToggler'];
  /** Whether to show the menu */
  showMenu?: App.Global.HeaderProps['showMenu'];
}

defineProps<Props>();

const appStore = useAppStore();
const themeStore = useThemeStore();
const authStore = useAuthStore();
const { isFullscreen, toggle } = useFullscreen();
const version = ref<Api.System.VersionRespVO>();

async function getVersion() {
  const { error, data } = await fetchGetLastVersion();
  if (error) {
    return;
  }
  if (data) {
    data.detailList = groupByPurposes(data?.detailList || [], 'purposes');
  }
  version.value = data || {};
}

function handleVersion() {
  renderModal(
    VersionModel,
    {
      version: version.value
    },
    {
      style: {
        width: '60%'
      }
    }
  );
}
onMounted(() => {
  getVersion();
  nextTick(() => {
    if (authStore.userInfo.isOriginalPassword && localStg.get('loginType') === 'pwd') {
      renderModal(
        changePwd,
        {},
        {
          title: '修改初始密码',
          style: {
            width: '30%'
          },
          closeOnEsc: false,
          closable: false,
          maskClosable: false
        }
      );
    }
  });
});
</script>

<template>
  <DarkModeContainer class="h-full flex-y-center px-12px shadow-header">
    <GlobalLogo v-if="showLogo" class="h-full" :style="{ width: themeStore.sider.width + 'px' }" />
    <MenuToggler v-if="showMenuToggler" :collapsed="appStore.siderCollapse" @click="appStore.toggleSiderCollapse" />
    <div v-if="showMenu" :id="GLOBAL_HEADER_MENU_ID" class="h-full flex-y-center flex-1-hidden"></div>
    <div v-else class="h-full flex-y-center flex-1-hidden">
      <GlobalBreadcrumb v-if="!appStore.isMobile" class="ml-12px" />
    </div>

    <div class="h-full flex-y-center justify-end">
      <div
        v-if="version?.versionNo"
        class="mr-1 cursor-pointer text-[15px] text-gray-500 hover:text-primary"
        @click="handleVersion"
      >
        v{{ version?.versionNo }}
      </div>
      <FullScreen v-if="!appStore.isMobile" :full="isFullscreen" @click="toggle" />
      <ThemeSchemaSwitch
        :theme-schema="themeStore.themeScheme"
        :is-dark="themeStore.darkMode"
        @switch="themeStore.toggleThemeScheme"
      />
      <ThemeButton />
      <UserAvatar />
    </div>
  </DarkModeContainer>
</template>

<style scoped></style>
