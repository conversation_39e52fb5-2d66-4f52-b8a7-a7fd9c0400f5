declare namespace PageState {
  /** 页面状态数据结构 */
  interface PageStateData {
    /** 表单字段值 */
    formValues: Record<string, any>;
    /** 分页信息 */
    pagination?: {
      current: number;
      pageSize: number;
    };
  }

  /** 页面状态存储映射 */
  interface PageStateMap {
    [routePath: string]: PageStateData;
  }

  /** URL参数同步配置 */
  interface UrlSyncConfig {
    /** 需要同步到URL的字段列表 */
    syncFields?: string[];
    /** 字段类型映射，用于URL参数类型转换 */
    fieldTypes?: Record<string, 'string' | 'number' | 'boolean' | 'array' | 'object'>;
    /** 是否启用URL同步 */
    enabled?: boolean;
  }

  /** 页面状态持久化配置 */
  interface PersistenceConfig {
    /** 页面唯一标识，默认使用路由路径 */
    pageKey?: string;
    /** URL同步配置 */
    urlSync?: UrlSyncConfig;
    /** 是否在页面初始化时自动恢复状态 */
    autoRestore?: boolean;
    /** 是否在状态恢复后自动触发搜索 */
    autoSearch?: boolean;
    /** 状态过期时间（毫秒），默认为会话期间有效 */
    expireTime?: number;
    /** 状态恢复时的回调函数 */
    onRestoreValues?: (values: Record<string, any>) => void;
  }

  /** 表单字段值变化事件 */
  interface FormValueChangeEvent {
    field: string;
    value: any;
    oldValue: any;
  }
}
