import { SetupStoreId } from '@/enum';
import { sessionStg } from '@/utils/storage';
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

export const usePageStateStore = defineStore(SetupStoreId.PageState, () => {
  // 页面状态映射
  const pageStates = ref<PageState.PageStateMap>({});

  // 从sessionStorage加载状态
  function loadStatesFromStorage() {
    const stored = sessionStg.get('pageStates');
    if (stored) {
      pageStates.value = stored;
    }
  }

  // 保存状态到sessionStorage
  function saveStatesToStorage() {
    sessionStg.set('pageStates', pageStates.value);
  }

  // 获取指定页面的状态
  function getPageState(pageKey: string): PageState.PageStateData | null {
    const state = pageStates.value[pageKey];
    return state || null;
  }

  // 保存页面状态
  function savePageState(pageKey: string, formValues: Record<string, any>, pagination?: { current: number; pageSize: number }) {
    const stateData: PageState.PageStateData = {
      formValues: { ...formValues },
      pagination: pagination ? { ...pagination } : undefined
    };

    pageStates.value[pageKey] = stateData;
    saveStatesToStorage();
  }

  // 更新页面表单值
  function updatePageFormValues(pageKey: string, formValues: Record<string, any>) {
    const existingState = pageStates.value[pageKey];
    if (existingState) {
      existingState.formValues = { ...formValues };
    } else {
      pageStates.value[pageKey] = {
        formValues: { ...formValues }
      };
    }
    saveStatesToStorage();
  }

  // 更新页面分页信息
  function updatePagePagination(pageKey: string, pagination: { current: number; pageSize: number }) {
    const existingState = pageStates.value[pageKey];
    if (existingState) {
      existingState.pagination = { ...pagination };
    } else {
      pageStates.value[pageKey] = {
        formValues: {},
        pagination: { ...pagination }
      };
    }
    saveStatesToStorage();
  }

  // 清除指定页面状态
  function clearPageState(pageKey: string) {
    delete pageStates.value[pageKey];
    saveStatesToStorage();
  }

  // 清除所有页面状态
  function clearAllStates() {
    pageStates.value = {};
    saveStatesToStorage();
  }

  // 获取所有页面状态（用于调试）
  const allPageStates = computed(() => pageStates.value);

  // 初始化时加载状态
  loadStatesFromStorage();

  return {
    // 状态
    allPageStates,

    // 方法
    getPageState,
    savePageState,
    updatePageFormValues,
    updatePagePagination,
    clearPageState,
    clearAllStates,
    loadStatesFromStorage,
    saveStatesToStorage
  };
});
