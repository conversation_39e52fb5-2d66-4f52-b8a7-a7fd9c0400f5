import type { SelectProps } from 'naive-ui';
import type { BaseFieldProps, ProFieldSharedSlots } from 'pro-naive-ui';
import { ProField, proFieldSharedProps, useOverrideProps } from 'pro-naive-ui';
import type { SlotsType } from 'vue';

import DeptTreeSelect from '../dept-tree-select.vue';

export default defineComponent({
  name: 'ProDeptTreeSelect',
  props: {
    /** 应该继承公共的属性 */
    ...proFieldSharedProps,
    /** InputProps 就是你这个组件的 props，外界使用时通过 fieldProps 传递 */
    fieldProps: Object as PropType<BaseFieldProps<SelectProps>>
  },
  /** 这里你应该继承公共的插槽 */
  slots: Object as SlotsType<ProFieldSharedSlots<SelectProps>>,
  setup(props) {
    /** 允许该组件的 props 可以被 pro-config-provider 重写 */
    const overridedProps = useOverrideProps('ProDeptTreeSelect', props);
    return {
      overridedProps
    };
  },
  render() {
    return (
      <ProField {...this.overridedProps}>
        {{
          ...this.$slots, // 透传公共的插槽
          input: (options: { inputProps: SelectProps; readonly: boolean }) => {
            return <DeptTreeSelect {...(options.inputProps as any)}  v-slots={this.$slots}></DeptTreeSelect>;
          }
        }}
      </ProField>
    );
  }
});
