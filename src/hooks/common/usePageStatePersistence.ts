import { usePageStateStore } from '@/store/modules/page-state';
import { nextTick, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useUrlSync } from './useUrlSync';

/**
 * 页面状态持久化Hook
 * 整合Store和URL同步，提供统一的页面状态管理接口
 */
export function usePageStatePersistence(
  searchForm: any,
  config: PageState.PersistenceConfig = {}
) {
  const route = useRoute();
  const pageStateStore = usePageStateStore();

  const {
    pageKey = route.path,
    urlSync = { enabled: true },
    autoRestore = true,
    autoSearch = true,
    onRestoreValues
  } = config;

  // 状态恢复完成标志
  const isRestored = ref(false);
  // 是否需要自动搜索
  const needAutoSearch = ref(false);

  // URL参数读取功能（仅读取，不同步）
  const { restoreFromUrl } = useUrlSync(ref({}), { ...urlSync, enabled: false });

  // 保存当前页面状态
  function saveCurrentState(pagination?: { current: number; pageSize: number }) {
    const formValues = searchForm.fieldsValue.value || {};
    pageStateStore.savePageState(pageKey, formValues, pagination);
  }

  // 恢复页面状态
  function restorePageState(): boolean {
    try {
      console.log('开始恢复页面状态，pageKey:', pageKey);

      // 1. 首先尝试从URL参数恢复
      const urlValues = restoreFromUrl();
      let hasUrlValues = Object.keys(urlValues).length > 0;
      console.log('URL参数值:', urlValues, '是否有值:', hasUrlValues);

      // 2. 从Store恢复状态
      const savedState = pageStateStore.getPageState(pageKey);
      let hasStoredValues = false;
      console.log('Store中的状态:', savedState);

      if (savedState && savedState.formValues) {
        hasStoredValues = Object.keys(savedState.formValues).length > 0;
      }

      // 3. 合并状态（URL参数优先级更高）
      let mergedValues = {};
      if (hasStoredValues) {
        mergedValues = { ...savedState!.formValues };
      }
      if (hasUrlValues) {
        mergedValues = { ...mergedValues, ...urlValues };
      }

      console.log('合并后的值:', mergedValues);

      // 4. 应用恢复的状态到表单
      if (Object.keys(mergedValues).length > 0) {
        // 过滤掉空值
        const filteredValues = Object.fromEntries(
          Object.entries(mergedValues).filter(([_, value]) =>
            value !== null && value !== undefined && value !== ''
          )
        );

        console.log('过滤后的值:', filteredValues);

        if (Object.keys(filteredValues).length > 0) {
          // 使用回调函数来设置表单值
          if (onRestoreValues) {
            onRestoreValues(filteredValues);
            console.log('已通过回调设置表单值');
          } else {
            console.warn('onRestoreValues 回调函数未提供');
          }
          needAutoSearch.value = autoSearch;
          return true;
        }
      }

      console.log('没有需要恢复的状态');
      return false;
    } catch (error) {
      console.warn('Failed to restore page state:', error);
      return false;
    }
  }

  // 清除当前页面状态
  function clearPageState() {
    pageStateStore.clearPageState(pageKey);
    searchForm.resetFields();
  }

  // 防抖保存函数
  let saveTimer: NodeJS.Timeout | null = null;

  function debouncedSave() {
    if (saveTimer) {
      clearTimeout(saveTimer);
    }
    saveTimer = setTimeout(() => {
      const formValues = searchForm.fieldsValue.value || {};
      pageStateStore.updatePageFormValues(pageKey, formValues);
    }, 300);
  }

  // 监听表单值变化，自动保存状态
  watch(
    () => searchForm.fieldsValue.value,
    (newValues) => {
      if (isRestored.value && newValues) {
        debouncedSave();
      }
    },
    { deep: true, immediate: false }
  );

  // 页面初始化时恢复状态
  onMounted(async () => {
    if (autoRestore) {
      await nextTick();
      const restored = restorePageState();
      isRestored.value = true;

      // 如果恢复了状态且需要自动搜索，触发搜索
      if (restored && needAutoSearch.value) {
        await nextTick();
        // 触发表单提交事件
        if (searchForm.submit) {
          searchForm.submit();
        }
      }
    } else {
      isRestored.value = true;
    }
  });

  // 提供给外部的API
  return {
    // 状态
    isRestored,
    needAutoSearch,

    // 方法
    saveCurrentState,
    restorePageState,
    clearPageState,

    // 工具方法
    updateFormValues: (values: Record<string, any>) => {
      searchForm.setFieldsValue(values);
      saveCurrentState();
    },

    // 获取当前状态
    getCurrentState: () => ({
      formValues: searchForm.fieldsValue.value || {},
      pageKey
    })
  };
}
