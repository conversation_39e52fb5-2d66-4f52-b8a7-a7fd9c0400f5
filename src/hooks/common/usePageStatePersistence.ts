import { usePageStateStore } from '@/store/modules/page-state';
import { nextTick, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useUrlSync } from './useUrlSync';

/**
 * 页面状态持久化Hook
 * 整合Store和URL同步，提供统一的页面状态管理接口
 */
export function usePageStatePersistence(
  searchForm: any,
  config: PageState.PersistenceConfig = {}
) {
  const route = useRoute();
  const pageStateStore = usePageStateStore();

  const {
    pageKey = route.path,
    urlSync = { enabled: true },
    autoRestore = true,
    autoSearch = true
  } = config;

  // 状态恢复完成标志
  const isRestored = ref(false);
  // 是否需要自动搜索
  const needAutoSearch = ref(false);

  // URL参数读取功能（仅读取，不同步）
  const { restoreFromUrl } = useUrlSync(ref({}), { ...urlSync, enabled: false });

  // 保存当前页面状态
  function saveCurrentState(pagination?: { current: number; pageSize: number }) {
    const formValues = searchForm.fieldsValue.value || {};
    pageStateStore.savePageState(pageKey, formValues, pagination);
  }

  // 恢复页面状态
  function restorePageState(): boolean {
    try {
      // 1. 首先尝试从URL参数恢复
      const urlValues = restoreFromUrl();
      let hasUrlValues = Object.keys(urlValues).length > 0;

      // 2. 从Store恢复状态
      const savedState = pageStateStore.getPageState(pageKey);
      let hasStoredValues = false;

      if (savedState && savedState.formValues) {
        hasStoredValues = Object.keys(savedState.formValues).length > 0;
      }

      // 3. 合并状态（URL参数优先级更高）
      let mergedValues = {};
      if (hasStoredValues) {
        mergedValues = { ...savedState!.formValues };
      }
      if (hasUrlValues) {
        mergedValues = { ...mergedValues, ...urlValues };
      }

      // 4. 应用恢复的状态到表单
      if (Object.keys(mergedValues).length > 0) {
        // 过滤掉空值
        const filteredValues = Object.fromEntries(
          Object.entries(mergedValues).filter(([_, value]) =>
            value !== null && value !== undefined && value !== ''
          )
        );

        if (Object.keys(filteredValues).length > 0) {
          searchForm.setFieldsValue(filteredValues);
          needAutoSearch.value = autoSearch;
          return true;
        }
      }

      return false;
    } catch (error) {
      console.warn('Failed to restore page state:', error);
      return false;
    }
  }

  // 清除当前页面状态
  function clearPageState() {
    pageStateStore.clearPageState(pageKey);
    searchForm.resetFields();
  }

  // 监听表单值变化，自动保存状态
  watch(
    () => searchForm.fieldsValue.value,
    (newValues) => {
      if (isRestored.value && newValues) {
        // 防抖保存，避免频繁操作
        setTimeout(() => {
          saveCurrentState();
        }, 500);
      }
    },
    { deep: true }
  );

  // 页面初始化时恢复状态
  onMounted(async () => {
    if (autoRestore) {
      await nextTick();
      const restored = restorePageState();
      isRestored.value = true;

      // 如果恢复了状态且需要自动搜索，触发搜索
      if (restored && needAutoSearch.value) {
        await nextTick();
        // 触发表单提交事件
        if (searchForm.submit) {
          searchForm.submit();
        }
      }
    } else {
      isRestored.value = true;
    }
  });

  // 提供给外部的API
  return {
    // 状态
    isRestored,
    needAutoSearch,

    // 方法
    saveCurrentState,
    restorePageState,
    clearPageState,

    // 工具方法
    updateFormValues: (values: Record<string, any>) => {
      searchForm.setFieldsValue(values);
      saveCurrentState();
    },

    // 获取当前状态
    getCurrentState: () => ({
      formValues: searchForm.fieldsValue.value || {},
      pageKey,
      lastUpdated: Date.now()
    })
  };
}
