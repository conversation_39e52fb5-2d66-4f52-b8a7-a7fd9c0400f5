import { usePageStateStore } from '@/store/modules/page-state';
import { watch } from 'vue';
import { useRoute } from 'vue-router';

/**
 * 简化的页面状态持久化Hook
 * 自动保存和恢复表单状态
 */
export function usePageStatePersistence(searchForm?: any) {
  const route = useRoute();
  const pageStateStore = usePageStateStore();
  const pageKey = route.path;

  // 获取保存的状态作为初始值
  function getInitialValues() {
    const savedState = pageStateStore.getPageState(pageKey);
    return savedState?.formValues || {};
  }

  // 保存表单状态
  function saveFormState(values: Record<string, any>, pagination?: { current: number; pageSize: number }) {
    pageStateStore.savePageState(pageKey, values, pagination);
  }

  // 清除状态
  function clearState() {
    pageStateStore.clearPageState(pageKey);
  }

  // 获取空的表单值对象
  function getEmptyFormValues(fieldNames: string[]) {
    const emptyValues: Record<string, any> = {};
    fieldNames.forEach(field => {
      emptyValues[field] = null;
    });
    return emptyValues;
  }

  // 如果提供了searchForm，监听表单值变化，自动保存
  if (searchForm) {
    watch(
      () => searchForm.fieldsValue?.value,
      (newValues) => {
        if (newValues && Object.keys(newValues).length > 0) {
          // 过滤掉空值
          const filteredValues = Object.fromEntries(
            Object.entries(newValues).filter(([_, value]) =>
              value !== null && value !== undefined && value !== ''
            )
          );
          if (Object.keys(filteredValues).length > 0) {
            saveFormState(filteredValues);
          }
        }
      },
      { deep: true }
    );
  }

  return {
    getInitialValues,
    saveFormState,
    clearState,
    getEmptyFormValues
  };
}
