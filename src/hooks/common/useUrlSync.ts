import { useRoute } from 'vue-router';

/**
 * URL参数读取Hook
 * 从URL参数读取并恢复表单字段值
 */
export function useUrlSync(
  _formValues: any, // 保留参数以兼容现有调用
  config: PageState.UrlSyncConfig = {}
) {
  const route = useRoute();

  const {
    syncFields = [],
    fieldTypes = {},
    enabled = true
  } = config;

  // 注意：URL更新功能已移除，仅保留读取功能

  // 注意：此hook现在仅用于从URL读取参数，不进行双向同步
  // 如果需要双向同步，可以取消注释以下代码：
  /*
  if (enabled) {
    watch(
      () => formValues.value,
      (newValues) => {
        if (newValues) {
          debouncedUpdateUrl(newValues);
        }
      },
      { deep: true }
    );
  }
  */

  // 从URL参数恢复表单值
  function restoreFromUrl(): Record<string, any> {
    if (!enabled) return {};

    const restored: Record<string, any> = {};
    const query = route.query;

    if (syncFields.length > 0) {
      // 只恢复指定字段
      syncFields.forEach(field => {
        const value = query[field];
        if (value !== undefined && value !== null) {
          restored[field] = deserializeValue(value as string | string[], fieldTypes[field]);
        }
      });
    } else {
      // 恢复所有URL参数
      Object.keys(query).forEach(field => {
        const value = query[field];
        if (value !== undefined && value !== null) {
          restored[field] = deserializeValue(value as string | string[], fieldTypes[field]);
        }
      });
    }

    return restored;
  }

  // 序列化值为字符串（用于URL参数）
  function serializeValue(value: any, type?: string): string {
    if (value === null || value === undefined) return '';

    switch (type) {
      case 'array':
        return Array.isArray(value) ? JSON.stringify(value) : String(value);
      case 'object':
        return typeof value === 'object' ? JSON.stringify(value) : String(value);
      case 'boolean':
        return String(Boolean(value));
      case 'number':
        return String(Number(value));
      default:
        return String(value);
    }
  }

  // 反序列化字符串为实际类型
  function deserializeValue(value: string | string[], type?: string): any {
    if (Array.isArray(value)) {
      value = value[0]; // 取第一个值
    }

    if (!value || value === '') return undefined;

    try {
      switch (type) {
        case 'array':
          return JSON.parse(value);
        case 'object':
          return JSON.parse(value);
        case 'boolean':
          return value === 'true';
        case 'number':
          const num = Number(value);
          return isNaN(num) ? undefined : num;
        default:
          return value;
      }
    } catch (error) {
      console.warn(`Failed to deserialize URL parameter: ${value}`, error);
      return value; // 返回原始字符串
    }
  }

  return {
    restoreFromUrl,
    serializeValue,
    deserializeValue
  };
}
