import { watch, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useDebounceFn } from '@vueuse/core';

/**
 * URL参数同步Hook
 * 实现表单字段与URL参数的双向同步
 */
export function useUrlSync(
  formValues: any,
  config: PageState.UrlSyncConfig = {}
) {
  const router = useRouter();
  const route = useRoute();

  const {
    syncFields = [],
    fieldTypes = {},
    enabled = true
  } = config;

  // 防抖更新URL，避免频繁操作
  const debouncedUpdateUrl = useDebounceFn((values: Record<string, any>) => {
    if (!enabled) return;

    const query = { ...route.query };
    
    // 更新需要同步的字段
    if (syncFields.length > 0) {
      syncFields.forEach(field => {
        const value = values[field];
        if (value !== undefined && value !== null && value !== '') {
          query[field] = serializeValue(value, fieldTypes[field]);
        } else {
          delete query[field];
        }
      });
    } else {
      // 如果没有指定字段，同步所有非空字段
      Object.keys(values).forEach(field => {
        const value = values[field];
        if (value !== undefined && value !== null && value !== '') {
          query[field] = serializeValue(value, fieldTypes[field]);
        } else {
          delete query[field];
        }
      });
    }

    // 使用replace避免产生历史记录
    router.replace({
      path: route.path,
      query
    });
  }, 300);

  // 监听表单值变化，同步到URL
  if (enabled) {
    watch(
      () => formValues.value,
      (newValues) => {
        if (newValues) {
          debouncedUpdateUrl(newValues);
        }
      },
      { deep: true }
    );
  }

  // 从URL参数恢复表单值
  function restoreFromUrl(): Record<string, any> {
    if (!enabled) return {};

    const restored: Record<string, any> = {};
    const query = route.query;

    if (syncFields.length > 0) {
      // 只恢复指定字段
      syncFields.forEach(field => {
        const value = query[field];
        if (value !== undefined) {
          restored[field] = deserializeValue(value, fieldTypes[field]);
        }
      });
    } else {
      // 恢复所有URL参数
      Object.keys(query).forEach(field => {
        const value = query[field];
        if (value !== undefined) {
          restored[field] = deserializeValue(value, fieldTypes[field]);
        }
      });
    }

    return restored;
  }

  // 序列化值为字符串（用于URL参数）
  function serializeValue(value: any, type?: string): string {
    if (value === null || value === undefined) return '';

    switch (type) {
      case 'array':
        return Array.isArray(value) ? JSON.stringify(value) : String(value);
      case 'object':
        return typeof value === 'object' ? JSON.stringify(value) : String(value);
      case 'boolean':
        return String(Boolean(value));
      case 'number':
        return String(Number(value));
      default:
        return String(value);
    }
  }

  // 反序列化字符串为实际类型
  function deserializeValue(value: string | string[], type?: string): any {
    if (Array.isArray(value)) {
      value = value[0]; // 取第一个值
    }

    if (!value || value === '') return undefined;

    try {
      switch (type) {
        case 'array':
          return JSON.parse(value);
        case 'object':
          return JSON.parse(value);
        case 'boolean':
          return value === 'true';
        case 'number':
          const num = Number(value);
          return isNaN(num) ? undefined : num;
        default:
          return value;
      }
    } catch (error) {
      console.warn(`Failed to deserialize URL parameter: ${value}`, error);
      return value; // 返回原始字符串
    }
  }

  return {
    restoreFromUrl,
    serializeValue,
    deserializeValue
  };
}
